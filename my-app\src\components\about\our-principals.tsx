import React from 'react';

const OurPrincipals = () => {
  const coreValues = [
    {
      title: "PROFESSIONALISM",
      description:
        "We always commit to do  our projects in a  workmanship like manner.",
    },
    {
      title: "TEAMWORK",
      description:
        "We know we can only  succeed together or fail  together as a team",
    },
    {
      title: "LEADERSHIP",
      description:
        "Everyone in our team is a  leader. We believe in  leading from the bottom up.",
    },
    {
      title: "INTEGRITY",
      description:
        "We believe in doing things  right & within the confines  of the laws of the land",
    },
    {
      title: "HONESTY",
      description:
        "We always strive to be  honest to our clients in  project delivery",
    },
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4 lg:px-8 max-w-4xl">
        {/* Decorative line at top */}
        <div className="flex justify-center mb-12">
          <div className="w-0.5 h-16 bg-gray-300"></div>
        </div>

        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-light text-gray-400 tracking-wider mb-8">
            OUR PRINCIPALS
          </h2>
        </div>

        {/* Core Values List */}
        <div className="space-y-12">
          {coreValues.map((value, index) => (
            <div key={index} className="text-center">
              <h3 className="text-xl md:text-2xl font-semibold text-gray-800 mb-4 tracking-wide">
                {value.title}
              </h3>
              <p className="text-gray-600 leading-relaxed max-w-2xl mx-auto text-sm md:text-base">
                {value.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default OurPrincipals;