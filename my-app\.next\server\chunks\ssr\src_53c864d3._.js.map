{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/lib/utils.ts"], "sourcesContent": ["import { ClassValue, clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/ui/typewriter-effect-smooth.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\nexport const TypewriterEffectSmooth = ({\r\n  words,\r\n  className,\r\n  cursorClassName,\r\n}: {\r\n  words: {\r\n    text: string;\r\n    className?: string;\r\n  }[];\r\n  className?: string;\r\n  cursorClassName?: string;\r\n}) => {\r\n  const [displayedText, setDisplayedText] = useState(\"\");\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n\r\n  // Combine all words into one continuous text\r\n  const fullText = words.map(word => word.text).join(\" \");\r\n  const firstWordClass = words[0]?.className || \"\";\r\n\r\n  useEffect(() => {\r\n    const timeout = setTimeout(() => {\r\n      if (currentIndex < fullText.length) {\r\n        // Typing the full sentence\r\n        setDisplayedText(fullText.substring(0, currentIndex + 1));\r\n        setCurrentIndex(currentIndex + 1);\r\n      }\r\n      // Once fully typed, keep it displayed (no deleting or cycling)\r\n    }, 100); // Typing speed\r\n\r\n    return () => clearTimeout(timeout);\r\n  }, [currentIndex, fullText]);\r\n\r\n  return (\r\n    <div className={cn(\"flex items-center gap-1\", className)}>\r\n      <div className=\"text-xs sm:text-base md:text-xl lg:text-3xl xl:text-5xl font-bold\">\r\n        <span className={cn(\"text-blue-800 font-medium italic\", firstWordClass)}>\r\n          {displayedText}\r\n        </span>\r\n        <span \r\n          className={cn(\r\n            \"inline-block w-0.5 h-4 sm:h-6 xl:h-12 bg-blue-800 ml-1 animate-pulse\",\r\n            cursorClassName\r\n          )}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKO,MAAM,yBAAyB,CAAC,EACrC,KAAK,EACL,SAAS,EACT,eAAe,EAQhB;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAC;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAC;IAEjD,6CAA6C;IAC7C,MAAM,WAAW,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,EAAE,IAAI,CAAC;IACnD,MAAM,iBAAiB,KAAK,CAAC,EAAE,EAAE,aAAa;IAE9C,IAAA,kNAAS,EAAC;QACR,MAAM,UAAU,WAAW;YACzB,IAAI,eAAe,SAAS,MAAM,EAAE;gBAClC,2BAA2B;gBAC3B,iBAAiB,SAAS,SAAS,CAAC,GAAG,eAAe;gBACtD,gBAAgB,eAAe;YACjC;QACA,+DAA+D;QACjE,GAAG,MAAM,eAAe;QAExB,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAc;KAAS;IAE3B,qBACE,8OAAC;QAAI,WAAW,IAAA,yHAAE,EAAC,2BAA2B;kBAC5C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAK,WAAW,IAAA,yHAAE,EAAC,oCAAoC;8BACrD;;;;;;8BAEH,8OAAC;oBACC,WAAW,IAAA,yHAAE,EACX,wEACA;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { TypewriterEffectSmooth } from \"@/components/ui/typewriter-effect-smooth\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\n\r\nconst Hero: React.FC = () => {\r\n  const [isLoaded, setIsLoaded] = useState(false);\r\n  const [currentBg, setCurrentBg] = useState(0);\r\n\r\n  // Typewriter animation words\r\n  const words = [\r\n    { text: \"Breathe\", className: \"text-white font-bold\" },\r\n    { text: \"Easy...\", className: \"text-blue-400 font-bold\" },\r\n    { text: \"Live\", className: \"text-white font-bold\" },\r\n    { text: \"Easy...\", className: \"text-cyan-400 font-bold\" },\r\n  ];\r\n\r\n  // Background rotation\r\n  const backgrounds = [\"/background3.jpg\", \"/background2.jpg\"];\r\n\r\n  useEffect(() => {\r\n    setIsLoaded(true);\r\n    const bgInterval = setInterval(() => {\r\n      setCurrentBg((prev) => (prev + 1) % backgrounds.length);\r\n    }, 8000);\r\n    return () => clearInterval(bgInterval);\r\n  }, [backgrounds.length]);\r\n\r\n  return (\r\n    <section className=\"relative min-h-screen rounded-xl mx-14 flex items-center justify-center overflow-hidden\">\r\n      {/* Background with Parallax Effect */}\r\n      <div className=\"absolute inset-0 bg-fixed\">\r\n        {backgrounds.map((bg, index) => (\r\n          <div\r\n            key={index}\r\n            className={`absolute inset-0 transition-all duration-2000 ${\r\n              index === currentBg\r\n                ? \"opacity-100 scale-100\"\r\n                : \"opacity-0 scale-105\"\r\n            }`}\r\n          >\r\n            <Image\r\n              src={bg}\r\n              alt=\"HVAC Background\"\r\n              fill\r\n              className=\"object-cover\"\r\n              priority={index === 0}\r\n            />\r\n          </div>\r\n        ))}\r\n\r\n        {/* Modern Overlay */}\r\n        <div className=\"absolute inset-0 bg-transparent \" />\r\n      </div>\r\n\r\n      {/* Main Content */}\r\n      <div\r\n        className={`relative z-10 w-full max-w-6xl mx-auto px-6 transition-all duration-1000 ${\r\n          isLoaded ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-8\"\r\n        }`}\r\n      >\r\n        {/* Hero Grid Layout */}\r\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]\">\r\n          {/* Left Column - Content */}\r\n          <div className=\"text-white space-y-8\">\r\n            {/* Main Heading */}\r\n            <h1 className=\"text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight\">\r\n              <span className=\"text-white\">ST HVAC  SALES & SERVICES</span>\r\n            </h1>\r\n\r\n            {/* Typewriter */}\r\n            <div className=\"text-xl sm:text-2xl\">\r\n              <TypewriterEffectSmooth\r\n                words={words}\r\n                className=\"text-xl sm:text-2xl lg:text-3xl\"\r\n                cursorClassName=\"bg-cyan-400\"\r\n              />\r\n            </div>\r\n\r\n            {/* Description */}\r\n            <p className=\"text-lg text-gray-300 leading-relaxed max-w-lg\">\r\n              Expert heating, cooling, and air quality solutions. From emergency\r\n              repairs to complete system installations, we deliver reliable\r\n              comfort for your home and business.\r\n            </p>\r\n\r\n            {/* Action Buttons */}\r\n            <div className=\"flex flex-col sm:flex-row gap-4\">\r\n              <Link\r\n                href=\"/about\"\r\n                className=\"px-8 py-4 bg-blue-600  text-white rounded-lg font-semibold transition-all duration-300 flex items-center justify-center gap-3 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\r\n              >\r\n                Learn More\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Background Indicators */}\r\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2 z-20\">\r\n        {backgrounds.map((_, index) => (\r\n          <button\r\n            key={index}\r\n            onClick={() => setCurrentBg(index)}\r\n            className={`w-2 h-2 rounded-full transition-all duration-300 ${\r\n              index === currentBg\r\n                ? \"bg-blue-600 w-8\"\r\n                : \"bg-white/40 hover:bg-white/60\"\r\n            }`}\r\n          />\r\n        ))}\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Hero;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,OAAiB;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAC;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;IAE3C,6BAA6B;IAC7B,MAAM,QAAQ;QACZ;YAAE,MAAM;YAAW,WAAW;QAAuB;QACrD;YAAE,MAAM;YAAW,WAAW;QAA0B;QACxD;YAAE,MAAM;YAAQ,WAAW;QAAuB;QAClD;YAAE,MAAM;YAAW,WAAW;QAA0B;KACzD;IAED,sBAAsB;IACtB,MAAM,cAAc;QAAC;QAAoB;KAAmB;IAE5D,IAAA,kNAAS,EAAC;QACR,YAAY;QACZ,MAAM,aAAa,YAAY;YAC7B,aAAa,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,YAAY,MAAM;QACxD,GAAG;QACH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,YAAY,MAAM;KAAC;IAEvB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;oBACZ,YAAY,GAAG,CAAC,CAAC,IAAI,sBACpB,8OAAC;4BAEC,WAAW,CAAC,8CAA8C,EACxD,UAAU,YACN,0BACA,uBACJ;sCAEF,cAAA,8OAAC,wIAAK;gCACJ,KAAK;gCACL,KAAI;gCACJ,IAAI;gCACJ,WAAU;gCACV,UAAU,UAAU;;;;;;2BAZjB;;;;;kCAkBT,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBACC,WAAW,CAAC,yEAAyE,EACnF,WAAW,8BAA8B,2BACzC;0BAGF,cAAA,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAK,WAAU;8CAAa;;;;;;;;;;;0CAI/B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oLAAsB;oCACrB,OAAO;oCACP,WAAU;oCACV,iBAAgB;;;;;;;;;;;0CAKpB,8OAAC;gCAAE,WAAU;0CAAiD;;;;;;0CAO9D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,uKAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,GAAG,sBACnB,8OAAC;wBAEC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,YACN,oBACA,iCACJ;uBANG;;;;;;;;;;;;;;;;AAYjB;uCAEe", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Image from 'next/image'\n\nconst Client = () => {\n  const clientImages = Array.from({ length: 29 }, (_, i) => `client${i + 1}.jpg`)\n\n  // Duplicate the array to create seamless loop\n  const duplicatedImages = [...clientImages, ...clientImages]\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Our Clients</h2>\n          <p className=\"text-lg text-gray-600\">\n            Trusted by leading companies and organizations\n          </p>\n        </div>\n\n        <div className=\"relative overflow-hidden\">\n          <div className=\"flex animate-scroll\">\n            {duplicatedImages.map((image, index) => (\n              <div\n                key={index}\n                className=\"flex-shrink-0 w-32 h-20 mx-4 relative\"\n              >\n                <Image\n                  src={`/${image}`}\n                  alt={`Client ${(index % clientImages.length) + 1}`}\n                  fill\n                  className=\"object-contain filter grayscale hover:grayscale-0 transition-all duration-300\"\n                  sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n                />\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default Client"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAKA,MAAM,SAAS;IACb,MAAM,eAAe,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,IAAM,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;IAE9E,8CAA8C;IAC9C,MAAM,mBAAmB;WAAI;WAAiB;KAAa;IAE3D,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,8OAAC;gCAEC,WAAU;0CAEV,cAAA,8OAAC,wIAAK;oCACJ,KAAK,CAAC,CAAC,EAAE,OAAO;oCAChB,KAAK,CAAC,OAAO,EAAE,AAAC,QAAQ,aAAa,MAAM,GAAI,GAAG;oCAClD,IAAI;oCACJ,WAAU;oCACV,OAAM;;;;;;+BARH;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBrB;uCAEe", "debugId": null}}, {"offset": {"line": 376, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/ui/layout-grid.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport Image from \"next/image\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst MotionImage = motion(Image);\r\n\r\ntype Card = {\r\n  id: number;\r\n  content: React.ReactNode | string;\r\n  className: string;\r\n  thumbnail: string;\r\n};\r\n\r\nexport const LayoutGrid = ({ cards }: { cards: Card[] }) => {\r\n  const [selected, setSelected] = useState<Card | null>(null);\r\n  const [lastSelected, setLastSelected] = useState<Card | null>(null);\r\n\r\n  const handleClick = (card: Card) => {\r\n    setLastSelected(selected);\r\n    setSelected(card);\r\n  };\r\n\r\n  const handleOutsideClick = () => {\r\n    setLastSelected(selected);\r\n    setSelected(null);\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full h-full p-10 grid grid-cols-1 md:grid-cols-3 max-w-7xl mx-auto gap-4 relative\">\r\n      {cards.map((card) => (\r\n        <div key={card.id} className={cn(card.className)}>\r\n          <motion.div\r\n            onClick={() => handleClick(card)}\r\n            layoutId={`card-${card.id}`}\r\n            className={cn(\r\n              card.className,\r\n              \"relative overflow-hidden cursor-pointer group\",\r\n              selected?.id === card.id\r\n                ? \"rounded-lg absolute inset-0 z-50 flex justify-center items-center flex-col\"\r\n                : lastSelected?.id === card.id\r\n                ? \"z-40 bg-white rounded-xl h-full w-full\"\r\n                : \"bg-white rounded-xl h-full w-full\"\r\n            )}\r\n            transition={{ layout: { duration: 0.5, ease: \"easeInOut\" } }}\r\n          >\r\n            {selected?.id === card.id && <SelectedCard selected={selected} />}\r\n            <ImageComponent card={card} />\r\n          </motion.div>\r\n        </div>\r\n      ))}\r\n\r\n      {/* Dimmed overlay when image is selected */}\r\n      <motion.div\r\n        onClick={handleOutsideClick}\r\n        className={cn(\r\n          \"absolute h-full w-full left-0 top-0 bg-black opacity-0 z-10\",\r\n          selected?.id ? \"pointer-events-auto\" : \"pointer-events-none\"\r\n        )}\r\n        animate={{ opacity: selected?.id ? 0.4 : 0 }}\r\n        transition={{ duration: 0.3 }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\n// 🔹 Animated Image Component\r\nconst ImageComponent = ({ card }: { card: Card }) => {\r\n  return (\r\n    <Image\r\n      src={card.thumbnail}\r\n      alt=\"Gallery Thumbnail\"\r\n      fill\r\n      className=\"object-cover object-center rounded-xl\"\r\n    />\r\n  );\r\n};\r\n\r\n// 🔹 Card Text Overlay on click\r\nconst SelectedCard = ({ selected }: { selected: Card | null }) => {\r\n  return (\r\n    <div className=\"bg-transparent h-full w-full flex flex-col justify-end rounded-lg shadow-2xl relative z-[60]\">\r\n      {/* Dark overlay behind text */}\r\n      <motion.div\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: 0.6 }}\r\n        className=\"absolute inset-0 h-full w-full bg-black z-10\"\r\n      />\r\n\r\n      {/* Text content animation */}\r\n      <motion.div\r\n        layoutId={`content-${selected?.id}`}\r\n        initial={{ opacity: 0, y: 100 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        exit={{ opacity: 0, y: 100 }}\r\n        transition={{ duration: 0.3, ease: \"easeInOut\" }}\r\n        className=\"relative px-8 pb-6 z-[70] text-white\"\r\n      >\r\n        {selected?.content}\r\n      </motion.div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AAJA;;;;;;AAMA,MAAM,cAAc,IAAA,oMAAM,EAAC,wIAAK;AASzB,MAAM,aAAa,CAAC,EAAE,KAAK,EAAqB;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAc;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAc;IAE9D,MAAM,cAAc,CAAC;QACnB,gBAAgB;QAChB,YAAY;IACd;IAEA,MAAM,qBAAqB;QACzB,gBAAgB;QAChB,YAAY;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;oBAAkB,WAAW,IAAA,yHAAE,EAAC,KAAK,SAAS;8BAC7C,cAAA,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS,IAAM,YAAY;wBAC3B,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;wBAC3B,WAAW,IAAA,yHAAE,EACX,KAAK,SAAS,EACd,iDACA,UAAU,OAAO,KAAK,EAAE,GACpB,+EACA,cAAc,OAAO,KAAK,EAAE,GAC5B,2CACA;wBAEN,YAAY;4BAAE,QAAQ;gCAAE,UAAU;gCAAK,MAAM;4BAAY;wBAAE;;4BAE1D,UAAU,OAAO,KAAK,EAAE,kBAAI,8OAAC;gCAAa,UAAU;;;;;;0CACrD,8OAAC;gCAAe,MAAM;;;;;;;;;;;;mBAhBhB,KAAK,EAAE;;;;;0BAsBnB,8OAAC,oMAAM,CAAC,GAAG;gBACT,SAAS;gBACT,WAAW,IAAA,yHAAE,EACX,+DACA,UAAU,KAAK,wBAAwB;gBAEzC,SAAS;oBAAE,SAAS,UAAU,KAAK,MAAM;gBAAE;gBAC3C,YAAY;oBAAE,UAAU;gBAAI;;;;;;;;;;;;AAIpC;AAEA,8BAA8B;AAC9B,MAAM,iBAAiB,CAAC,EAAE,IAAI,EAAkB;IAC9C,qBACE,8OAAC,wIAAK;QACJ,KAAK,KAAK,SAAS;QACnB,KAAI;QACJ,IAAI;QACJ,WAAU;;;;;;AAGhB;AAEA,gCAAgC;AAChC,MAAM,eAAe,CAAC,EAAE,QAAQ,EAA6B;IAC3D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,oMAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAI;gBACxB,WAAU;;;;;;0BAIZ,8OAAC,oMAAM,CAAC,GAAG;gBACT,UAAU,CAAC,QAAQ,EAAE,UAAU,IAAI;gBACnC,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAI;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,MAAM;oBAAE,SAAS;oBAAG,GAAG;gBAAI;gBAC3B,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAY;gBAC/C,WAAU;0BAET,UAAU;;;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { LayoutGrid } from \"@/components/ui/layout-grid\";\r\n\r\nexport default function Gallery() {\r\n  return (\r\n    <section className=\"relative py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50 overflow-hidden\">\r\n      {/* Background Blobs */}\r\n      <div className=\"absolute inset-0 opacity-30 pointer-events-none\">\r\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse\"></div>\r\n        <div className=\"absolute bottom-20 right-10 w-72 h-72 bg-cyan-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-1000\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        {/* Section Header */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 40 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-16\"\r\n        >\r\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\r\n            OUR <span className=\"text-blue-600\">PROJECT</span> GALLERY\r\n          </h2>\r\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\r\n            Explore our portfolio of successful HVAC installations and\r\n            energy-efficient projects.\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Gallery Grid */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 50 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8, delay: 0.2 }}\r\n          viewport={{ once: true }}\r\n          className=\"min-h-screen\"\r\n        >\r\n          <LayoutGrid cards={cards} />\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\n//\r\n// 🔹 Text Blocks for Each Card\r\n//\r\nconst SkeletonOne = () => (\r\n  <div>\r\n    <p className=\"font-bold md:text-4xl text-xl text-white\">\r\n      Commercial HVAC Installation\r\n    </p>\r\n    <p className=\"font-normal text-base text-white\">\r\n      Downtown Office Complex — 2024\r\n    </p>\r\n    <p className=\"font-normal text-base my-4 max-w-lg text-neutral-200\">\r\n      Complete HVAC system setup for a 50,000 sq ft office building, featuring\r\n      high-efficiency units and smart climate control systems.\r\n    </p>\r\n  </div>\r\n);\r\n\r\nconst SkeletonTwo = () => (\r\n  <div>\r\n    <p className=\"font-bold md:text-4xl text-xl text-white\">\r\n      Industrial Ductwork System\r\n    </p>\r\n    <p className=\"font-normal text-base text-white\">\r\n      Manufacturing Plant — 2023\r\n    </p>\r\n    <p className=\"font-normal text-base my-4 max-w-lg text-neutral-200\">\r\n      Custom-engineered ductwork and ventilation designed for heavy-duty\r\n      manufacturing environments.\r\n    </p>\r\n  </div>\r\n);\r\n\r\nconst SkeletonThree = () => (\r\n  <div>\r\n    <p className=\"font-bold md:text-4xl text-xl text-white\">\r\n      Smart Thermostat Installation\r\n    </p>\r\n    <p className=\"font-normal text-base text-white\">Residential Home — 2024</p>\r\n    <p className=\"font-normal text-base my-4 max-w-lg text-neutral-200\">\r\n      Integrated smart thermostat system with app-based monitoring and energy\r\n      optimization.\r\n    </p>\r\n  </div>\r\n);\r\n\r\nconst SkeletonFour = () => (\r\n  <div>\r\n    <p className=\"font-bold md:text-4xl text-xl text-white\">\r\n      Residential HVAC Upgrade\r\n    </p>\r\n    <p className=\"font-normal text-base text-white\">Family Residence — 2024</p>\r\n    <p className=\"font-normal text-base my-4 max-w-lg text-neutral-200\">\r\n      Complete upgrade to high-efficiency units with improved indoor air quality\r\n      and quieter performance.\r\n    </p>\r\n  </div>\r\n);\r\n\r\nconst SkeletonFive = () => (\r\n  <div>\r\n    <p className=\"font-bold md:text-4xl text-xl text-white\">\r\n      Preventive Maintenance Service\r\n    </p>\r\n    <p className=\"font-normal text-base text-white\">Various Locations — 2024</p>\r\n    <p className=\"font-normal text-base my-4 max-w-lg text-neutral-200\">\r\n      Comprehensive maintenance plans including inspection, cleaning, and\r\n      performance optimization.\r\n    </p>\r\n  </div>\r\n);\r\n\r\nconst SkeletonSix = () => (\r\n  <div>\r\n    <p className=\"font-bold md:text-4xl text-xl text-white\">\r\n      Energy Efficiency Retrofit\r\n    </p>\r\n    <p className=\"font-normal text-base text-white\">Retail Center — 2023</p>\r\n    <p className=\"font-normal text-base my-4 max-w-lg text-neutral-200\">\r\n      Energy-efficiency retrofitting project achieving 30 % reduction in overall\r\n      HVAC power usage.\r\n    </p>\r\n  </div>\r\n);\r\n\r\n//\r\n// 🔹 Card Data (connects to layout-grid.tsx)\r\n//\r\nconst cards = [\r\n  {\r\n    id: 1,\r\n    content: <SkeletonOne />,\r\n    className: \"md:col-span-2\",\r\n    thumbnail: \"/background2.jpg\",\r\n  },\r\n  {\r\n    id: 2,\r\n    content: <SkeletonTwo />,\r\n    className: \"col-span-1\",\r\n    thumbnail: \"/background3.jpg\",\r\n  },\r\n  {\r\n    id: 3,\r\n    content: <SkeletonThree />,\r\n    className: \"col-span-1\",\r\n    thumbnail: \"/background2.jpg\",\r\n  },\r\n  {\r\n    id: 4,\r\n    content: <SkeletonFour />,\r\n    className: \"md:col-span-2\",\r\n    thumbnail: \"/background.jpg\",\r\n  },\r\n  {\r\n    id: 5,\r\n    content: <SkeletonFive />,\r\n    className: \"col-span-1\",\r\n    thumbnail: \"/Picture8.jpg\",\r\n  },\r\n  {\r\n    id: 6,\r\n    content: <SkeletonSix />,\r\n    className: \"col-span-1\",\r\n    thumbnail: \"/Picture11.jpg\",\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAU;wBAC7C,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;;oCAAoD;kDAC5D,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAc;;;;;;;0CAEpD,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC,wJAAU;4BAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;AAK7B;AAEA,EAAE;AACF,+BAA+B;AAC/B,EAAE;AACF,MAAM,cAAc,kBAClB,8OAAC;;0BACC,8OAAC;gBAAE,WAAU;0BAA2C;;;;;;0BAGxD,8OAAC;gBAAE,WAAU;0BAAmC;;;;;;0BAGhD,8OAAC;gBAAE,WAAU;0BAAuD;;;;;;;;;;;;AAOxE,MAAM,cAAc,kBAClB,8OAAC;;0BACC,8OAAC;gBAAE,WAAU;0BAA2C;;;;;;0BAGxD,8OAAC;gBAAE,WAAU;0BAAmC;;;;;;0BAGhD,8OAAC;gBAAE,WAAU;0BAAuD;;;;;;;;;;;;AAOxE,MAAM,gBAAgB,kBACpB,8OAAC;;0BACC,8OAAC;gBAAE,WAAU;0BAA2C;;;;;;0BAGxD,8OAAC;gBAAE,WAAU;0BAAmC;;;;;;0BAChD,8OAAC;gBAAE,WAAU;0BAAuD;;;;;;;;;;;;AAOxE,MAAM,eAAe,kBACnB,8OAAC;;0BACC,8OAAC;gBAAE,WAAU;0BAA2C;;;;;;0BAGxD,8OAAC;gBAAE,WAAU;0BAAmC;;;;;;0BAChD,8OAAC;gBAAE,WAAU;0BAAuD;;;;;;;;;;;;AAOxE,MAAM,eAAe,kBACnB,8OAAC;;0BACC,8OAAC;gBAAE,WAAU;0BAA2C;;;;;;0BAGxD,8OAAC;gBAAE,WAAU;0BAAmC;;;;;;0BAChD,8OAAC;gBAAE,WAAU;0BAAuD;;;;;;;;;;;;AAOxE,MAAM,cAAc,kBAClB,8OAAC;;0BACC,8OAAC;gBAAE,WAAU;0BAA2C;;;;;;0BAGxD,8OAAC;gBAAE,WAAU;0BAAmC;;;;;;0BAChD,8OAAC;gBAAE,WAAU;0BAAuD;;;;;;;;;;;;AAOxE,EAAE;AACF,6CAA6C;AAC7C,EAAE;AACF,MAAM,QAAQ;IACZ;QACE,IAAI;QACJ,uBAAS,8OAAC;;;;;QACV,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,uBAAS,8OAAC;;;;;QACV,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,uBAAS,8OAAC;;;;;QACV,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,uBAAS,8OAAC;;;;;QACV,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,uBAAS,8OAAC;;;;;QACV,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,uBAAS,8OAAC;;;;;QACV,WAAW;QACX,WAAW;IACb;CACD", "debugId": null}}, {"offset": {"line": 930, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from 'next/link'\r\nimport Image from 'next/image'\r\nimport React from 'react'\r\nimport { motion } from 'framer-motion'\r\n\r\nconst AboutTeaser = () => {\r\n const keyFeatures = [\r\n    {\r\n      title: \"Licensed Professionals\",\r\n      description: \"Our team consists of fully licensed HVAC technicians with extensive industry experience and ongoing training.\",\r\n      icon: (\r\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n        </svg>\r\n      )\r\n    },\r\n    {\r\n      title: \"24/7 Emergency Service\",\r\n      description: \"Round-the-clock emergency HVAC services to ensure your comfort is never compromised.\",\r\n      icon: (\r\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n        </svg>\r\n      )\r\n    },\r\n    {\r\n      title: \"Energy Efficient Solutions\",\r\n      description: \"Modern HVAC systems designed to reduce energy costs while maintaining optimal indoor comfort.\",\r\n      icon: (\r\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\r\n        </svg>\r\n      )\r\n    }\r\n  ];\r\n\r\n\r\n  return (\r\n    <section className=\"py-16 lg:py-24 bg-white\">\r\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center\">\r\n          {/* Left Content - Professional Images */}\r\n          <motion.div\r\n            initial={{ opacity: 0, x: -30 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"relative\"\r\n          >\r\n            {/* Main Professional Image */}\r\n            <div className=\"relative\">\r\n              <div className=\"relative w-5/6 h-[500px] lg:h-[600px] rounded-lg overflow-hidden shadow-lg\">\r\n                <Image\r\n                  src=\"/about1.jpg\"\r\n                  alt=\"Professional HVAC Technician at Work\"\r\n                  fill\r\n                  className=\"object-cover\"\r\n                  priority\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Secondary Professional Image */}\r\n            <motion.div\r\n              initial={{ opacity: 0, scale: 0.9 }}\r\n              whileInView={{ opacity: 1, scale: 1 }}\r\n              transition={{ duration: 0.6, delay: 0.3 }}\r\n              viewport={{ once: true }}\r\n              className=\"absolute top-3/4 right-6 w-4/6 h-[280px] lg:h-[240px] rounded-lg overflow-hidden shadow-lg\"\r\n            >\r\n              <Image\r\n                src=\"/about2.jpg\"\r\n                alt=\"HVAC System Installation\"\r\n                fill\r\n                className=\"object-cover\"\r\n              />\r\n            </motion.div>\r\n          </motion.div>\r\n\r\n          {/* Right Content */}\r\n          <motion.div\r\n            initial={{ opacity: 0, x: 50 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"space-y-8\"\r\n          >\r\n            {/* Professional Section Label */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"flex items-center space-x-2\"\r\n            >\r\n              <div className=\"w-12 h-px bg-blue-600\"></div>\r\n              <span className=\"text-blue-600 font-medium text-sm uppercase tracking-wider\">\r\n                About Our Company\r\n              </span>\r\n            </motion.div>\r\n\r\n            {/* Professional Heading */}\r\n            <motion.h2\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 leading-tight\"\r\n            >\r\n              Professional HVAC Services You Can Trust\r\n            </motion.h2>\r\n\r\n            {/* Company Stats */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.3 }}\r\n              viewport={{ once: true }}\r\n              className=\"grid grid-cols-2 gap-6\"\r\n            ></motion.div>\r\n\r\n            {/* Professional Key Features */}\r\n            <div className=\"space-y-4\">\r\n              {keyFeatures.map((feature, index) => (\r\n                <motion.div\r\n                  key={index}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  whileInView={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}\r\n                  viewport={{ once: true }}\r\n                  className=\"flex items-start gap-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-300\"\r\n                >\r\n                  <div className=\"flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center\">\r\n                    {feature.icon}\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <h4 className=\"text-lg font-semibold text-gray-900 mb-1\">\r\n                      {feature.title}\r\n                    </h4>\r\n                    <p className=\"text-gray-600 text-sm leading-relaxed\">\r\n                      {feature.description}\r\n                    </p>\r\n                  </div>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Professional CTA Button */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"pt-4\"\r\n            >\r\n              <Link\r\n                href=\"/about\"\r\n                className=\"inline-flex items-center gap-2 px-8 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-300\"\r\n              >\r\n                Learn More About Us\r\n                <svg\r\n                  className=\"w-4 h-4\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    strokeWidth={2}\r\n                    d=\"M17 8l4 4m0 0l-4 4m4-4H3\"\r\n                  />\r\n                </svg>\r\n              </Link>\r\n            </motion.div>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\nexport default AboutTeaser\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,cAAc;IACnB,MAAM,cAAc;QACjB;YACE,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;KACD;IAGD,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAGV,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wIAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,IAAI;wCACJ,WAAU;wCACV,QAAQ;;;;;;;;;;;;;;;;0CAMd,8OAAC,oMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAEV,cAAA,8OAAC,wIAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,IAAI;oCACJ,WAAU;;;;;;;;;;;;;;;;;kCAMhB,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAGV,8OAAC,oMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAA6D;;;;;;;;;;;;0CAM/E,8OAAC,oMAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;0CAKD,8OAAC,oMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;;;;;0CAIZ,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,SAAS,sBACzB,8OAAC,oMAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM,QAAQ;wCAAI;wCACtD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,IAAI;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAEhB,8OAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;;;;;;;;uCAfnB;;;;;;;;;;0CAuBX,8OAAC,oMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAEV,cAAA,8OAAC,uKAAI;oCACH,MAAK;oCACL,WAAU;;wCACX;sDAEC,8OAAC;4CACC,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtB;uCAEe", "debugId": null}}, {"offset": {"line": 1350, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport Image from 'next/image';\r\n\r\nexport function WhyChooseUsSection() {\r\n\r\n  return (\r\n    <section className=\"py-16 lg:py-24 bg-blue-50\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-14 lg:gap-20 items-center\">\r\n          <motion.div\r\n            initial={{ opacity: 0, x: -50 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"space-y-8\"\r\n          >\r\n            {/* Header */}\r\n            <div className=\"mb-12\">\r\n              <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4\">\r\n                Why Choose Us\r\n              </h1>\r\n              <p className=\"text-gray-600 text-lg\">\r\n                Our commitment to excellence and customer satisfaction\r\n              </p>\r\n            </div>\r\n\r\n            {/* Numbered Points Grid */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\r\n              {/* Point 01 */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"space-y-4\"\r\n              >\r\n                <div className=\"text-6xl font-black text-blue-400\">01</div>\r\n                <h3 className=\"text-xl font-bold text-gray-900\">\r\n                  Fast Breakdown Recovery\r\n                </h3>\r\n                <p className=\"text-gray-600 leading-relaxed\">\r\n                  <span className=\"font-semibold text-gray-900\">W</span>e understand the importance of breakdown recovery lead time to our customers.\r\n                </p>\r\n              </motion.div>\r\n\r\n              {/* Point 02 */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.2 }}\r\n                viewport={{ once: true }}\r\n                className=\"space-y-4\"\r\n              >\r\n                <div className=\"text-6xl font-black text-blue-400\">02</div>\r\n                <h3 className=\"text-xl font-bold text-gray-900\">\r\n                  Reliable System & Manpower\r\n                </h3>\r\n                <p className=\"text-gray-600 leading-relaxed\">\r\n                  <span className=\"font-semibold text-gray-900\">O</span>ur unique internal System, together with our dedicated manpower ensure that we deliver on time every time – We Won't Be Beaten.\r\n                </p>\r\n              </motion.div>\r\n\r\n              {/* Point 03 */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.3 }}\r\n                viewport={{ once: true }}\r\n                className=\"space-y-4\"\r\n              >\r\n                <div className=\"text-6xl font-black text-blue-400\">03</div>\r\n                <h3 className=\"text-xl font-bold text-gray-900\">\r\n                  ST HVAC Maintenance Method\r\n                </h3>\r\n                <p className=\"text-gray-600 leading-relaxed\">\r\n                  <span className=\"font-semibold text-gray-900\">O</span>ur unique 'ST HVAC Maintenance method is the quickest and safest process around, drastically reducing breakdown time and Maintenance costs.\r\n                </p>\r\n              </motion.div>\r\n\r\n              {/* Point 04 */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.4 }}\r\n                viewport={{ once: true }}\r\n                className=\"space-y-4\"\r\n              >\r\n                <div className=\"text-6xl font-black text-blue-400\">04</div>\r\n                <h3 className=\"text-xl font-bold text-gray-900\">\r\n                  Competitive Rates\r\n                </h3>\r\n                <p className=\"text-gray-600 leading-relaxed\">\r\n                  <span className=\"font-semibold text-gray-900\">O</span>ur Rates are very Competitive with no compromise attitude as far as quality is concerned.\r\n                </p>\r\n              </motion.div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Right Column - Large Image */}\r\n          <motion.div\r\n            initial={{ opacity: 0, x: 50 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.5 }}\r\n            viewport={{ once: true }}\r\n            className=\"relative\"\r\n          >\r\n            <div className=\"relative  w-5/6 h-[500px] lg:h-[600px] rounded-2xl overflow-hidden shadow-2xl\">\r\n              <Image\r\n                src=\"/background.jpg\"\r\n                alt=\"Professional HVAC Services\"\r\n                fill\r\n                className=\"object-cover\"\r\n              />             \r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n\r\n\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAMO,SAAS;IAEd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAGV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAMvC,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,oMAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DAAoC;;;;;;0DACnD,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAGhD,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAK,WAAU;kEAA8B;;;;;;oDAAQ;;;;;;;;;;;;;kDAK1D,8OAAC,oMAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DAAoC;;;;;;0DACnD,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAGhD,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAK,WAAU;kEAA8B;;;;;;oDAAQ;;;;;;;;;;;;;kDAK1D,8OAAC,oMAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DAAoC;;;;;;0DACnD,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAGhD,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAK,WAAU;kEAA8B;;;;;;oDAAQ;;;;;;;;;;;;;kDAK1D,8OAAC,oMAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DAAoC;;;;;;0DACnD,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAGhD,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAK,WAAU;kEAA8B;;;;;;oDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9D,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,wIAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,IAAI;gCACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B", "debugId": null}}, {"offset": {"line": 1718, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\n\nexport function SpecialtySection() {\n  const specialtyServices = [\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n        </svg>\n      ),\n      title: \"Commercial HVAC\",\n      description: \"Complete commercial heating and cooling solutions\"\n    },\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n        </svg>\n      ),\n      title: \"Residential HVAC\",\n      description: \"Home comfort solutions and energy efficiency\"\n    },\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n        </svg>\n      ),\n      title: \"Maintenance Plans\",\n      description: \"Preventive maintenance and service contracts\"\n    },\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n        </svg>\n      ),\n      title: \"Energy Audits\",\n      description: \"Efficiency assessments and optimization\"\n    }\n  ];\n\n  return (\n    <section className=\"py-16 lg:py-24 bg-white\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl\">\n        {/* Specialty Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"bg-gray-50 rounded-2xl p-8 lg:p-12 shadow-lg\"\n        >\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              <span className=\"text-blue-600\">SPECIALTY</span> WITHIN\n            </h2>\n            <div className=\"w-24 h-1 bg-blue-600 mx-auto\"></div>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n            {/* Left - Main Text */}\n            <div className=\"space-y-6\">\n              <div className=\"bg-blue-50 rounded-xl p-8 border border-blue-100\">\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4 leading-relaxed\">\n                  <span className=\"text-blue-600\">O</span>ur all locations are staffed by trained and well-equipped personnel. Rapid response and speedy results can be counted on. Easy access is further enhanced through Service Solutions Providers, minimizing down-time and ensuring quick satisfaction.\n                </h3>\n                <div className=\"grid grid-cols-2 gap-4 mt-6\">\n                  <div className=\"text-center p-4 bg-white rounded-lg shadow-sm\">\n                    <div className=\"text-2xl font-bold text-blue-600 mb-1\">24/7</div>\n                    <div className=\"text-sm text-gray-600\">Service Available</div>\n                  </div>\n                  <div className=\"text-center p-4 bg-white rounded-lg shadow-sm\">\n                    <div className=\"text-2xl font-bold text-blue-600 mb-1\">100%</div>\n                    <div className=\"text-sm text-gray-600\">Satisfaction Rate</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Right - Services Grid */}\n            <div className=\"grid grid-cols-2 gap-4\">\n              {specialtyServices.map((service, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  whileInView={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: 0.1 + index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"bg-white rounded-lg p-4 text-center hover:bg-blue-50 transition-colors duration-300 shadow-md\"\n                >\n                  <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3\">\n                    {service.icon}\n                  </div>\n                  <h4 className=\"font-semibold text-gray-900 text-sm mb-2\">\n                    {service.title}\n                  </h4>\n                  <p className=\"text-gray-600 text-xs\">\n                    {service.description}\n                  </p>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAMO,SAAS;IACd,MAAM,oBAAoB;QACxB;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;;kCAC/E,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;kCACrE,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBAEb,cAAA,8OAAC,oMAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,UAAU;oBAAE,MAAM;gBAAK;gBACvB,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAgB;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAQ;;;;;;;sDAE1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;sEACvD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;sEACvD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO/C,8OAAC;gCAAI,WAAU;0CACZ,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,8OAAC,oMAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM,QAAQ;wCAAI;wCACtD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,IAAI;;;;;;0DAEf,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,8OAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;;uCAdjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBvB", "debugId": null}}, {"offset": {"line": 2071, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport Image from 'next/image';\r\n\r\nexport function WhatWeOfferSection() {\r\n  const services = [\r\n    {\r\n      id: 1,\r\n      title: \"HVAC System Installation\",\r\n      description:\r\n        \"Heating, ventilation, andair conditioning (HVAC) system is designed to achieve the environmental requirements of the comfort of occupants and a process.\",\r\n      image: \"/offer1.jpg\",\r\n      category: \"Installation\",\r\n    },\r\n    {\r\n      id: 2,\r\n      title: \"Comfort Cooling Solutions\",\r\n      description:\r\n        \"These systems designed to give you  maximum comfort. Be it in an office  environment or even in a hotel.\",\r\n      image: \"/offer2.jpg\",\r\n      category: \"Cooling\",\r\n    },\r\n    {\r\n      id: 3,\r\n      title: \"Mechanical Ventilation\",\r\n      description:\r\n        \"These are ideal for factories where  ventilations is key for both goods and  factory workers\",\r\n      image: \"/offer3.jpg\",\r\n      category: \"Ventilation\",\r\n    },\r\n    {\r\n      id: 4,\r\n      title: \"Temperature Monitoring\",\r\n      description:\r\n        \"These are solutions designed for  sensitive areas where awareness of  temperature and humidity status at any  given time is of utmost importance.\",\r\n      image: \"/offer4.jpg\",\r\n      category: \"Monitoring\",\r\n    },\r\n    {\r\n      id: 5,\r\n      title: \"HVAC Plant Operations & Maintenance\",\r\n      description:\r\n        \"Operation and maintenance of HVAC plant maintaining log books, generating reports, making energy efficient system.\",\r\n      image: \"/offer5.jpg\",\r\n      category: \"Maintenance\",\r\n    },\r\n    {\r\n      id: 6,\r\n      title: \"Building Flush Out\",\r\n      description:\r\n        \"This is ideal for sensitive areas such as: Offices  data centers, hospital laboratories,  theatres e.t.c\",\r\n      image: \"/offer6.jpg\",\r\n      category: \"Air Quality\",\r\n    },\r\n  ];\r\n\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1\r\n      }\r\n    }\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { opacity: 0, y: 30 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        duration: 0.6\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <section className=\"relative py-20 bg-gray-50 overflow-hidden\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0 opacity-5\">\r\n        <div\r\n          className=\"absolute inset-0\"\r\n          style={{\r\n            backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-11.046-8.954-20-20-20v20h20z'/%3E%3C/g%3E%3C/svg%3E\")`,\r\n            backgroundSize: \"40px 40px\",\r\n          }}\r\n        ></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        {/* Header */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-16\"\r\n        >\r\n          <div className=\"flex items-center justify-center py-2\">\r\n            <div className=\"w-16 h-px bg-blue-600\"></div>\r\n            <span className=\"px-4 text-blue-600 font-medium text-sm uppercase tracking-wider\">Services</span>\r\n            <div className=\"w-16 h-px bg-blue-600\"></div>\r\n          </div>\r\n          <h2 className=\"text-4xl md:text-5xl font-bold tracking-tight text-gray-900 mb-6\">\r\n            What <span className=\"text-blue-600\">We</span> Offer\r\n          </h2>\r\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\r\n            Trust our expertise to deliver fast, reliable results every time.\r\n            Our skilled technicians ensure your comfort is never compromised\r\n            with professional solutions tailored to your needs.\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Services Grid - 3 Cards per Row */}\r\n        <motion.div\r\n          variants={containerVariants}\r\n          initial=\"hidden\"\r\n          whileInView=\"visible\"\r\n          viewport={{ once: true }}\r\n          className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12\"\r\n        >\r\n          {services.slice(0, 3).map((service) => (\r\n            <motion.div\r\n              key={service.id}\r\n              variants={itemVariants}\r\n              className=\"group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100\"\r\n            >\r\n              {/* Horizontal Card Layout */}\r\n              <div className=\"flex flex-col h-full\">\r\n                {/* Image Section */}\r\n                <div className=\"relative h-48 overflow-hidden\">\r\n                  <Image\r\n                    src={service.image}\r\n                    alt={service.title}\r\n                    width={400}\r\n                    height={200}\r\n                    className=\"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\"\r\n                  />\r\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent\"></div>\r\n                  <div className=\"absolute top-4 left-4\">\r\n                    <span className=\"bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-semibold\">\r\n                      {service.category}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Content Section */}\r\n                <div className=\"p-6 flex-1 flex flex-col\">\r\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300\">\r\n                    {service.title}\r\n                  </h3>\r\n                  <p className=\"text-gray-600 mb-4 leading-relaxed flex-1\">\r\n                    {service.description}\r\n                  </p>\r\n                  {/* Action Button */}\r\n                  <button className=\"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-semibold transition-colors duration-300 mt-auto\">\r\n                    Learn More\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </motion.div>\r\n\r\n        {/* Second Row - 3 Cards */}\r\n        <motion.div\r\n          variants={containerVariants}\r\n          initial=\"hidden\"\r\n          whileInView=\"visible\"\r\n          viewport={{ once: true }}\r\n          className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\"\r\n        >\r\n          {services.slice(3, 6).map((service) => (\r\n            <motion.div\r\n              key={service.id}\r\n              variants={itemVariants}\r\n              className=\"group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100\"\r\n            >\r\n              {/* Horizontal Card Layout */}\r\n              <div className=\"flex flex-col h-full\">\r\n                {/* Image Section */}\r\n                <div className=\"relative h-48 overflow-hidden\">\r\n                  <Image\r\n                    src={service.image}\r\n                    alt={service.title}\r\n                    width={400}\r\n                    height={200}\r\n                    className=\"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\"\r\n                  />\r\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent\"></div>\r\n                  <div className=\"absolute top-4 left-4\">\r\n                    <span className=\"bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-semibold\">\r\n                      {service.category}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                {/* Content Section */}\r\n                <div className=\"p-6 flex-1 flex flex-col\">\r\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300\">\r\n                    {service.title}\r\n                  </h3>\r\n                  <p className=\"text-gray-600 mb-4 leading-relaxed flex-1\">\r\n                    {service.description}\r\n                  </p>\r\n                  {/* Action Button */}\r\n                  <button className=\"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-semibold transition-colors duration-300 mt-auto\">\r\n                    Learn More\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAMO,SAAS;IACd,MAAM,WAAW;QACf;YACE,IAAI;YACJ,OAAO;YACP,aACE;YACF,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aACE;YACF,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aACE;YACF,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aACE;YACF,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aACE;YACF,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aACE;YACF,OAAO;YACP,UAAU;QACZ;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBACL,iBAAiB,CAAC,mOAAmO,CAAC;wBACtP,gBAAgB;oBAClB;;;;;;;;;;;0BAIJ,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAAkE;;;;;;kDAClF,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAEjB,8OAAC;gCAAG,WAAU;;oCAAmE;kDAC1E,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAS;;;;;;;0CAEhD,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAQzE,8OAAC,oMAAM,CAAC,GAAG;wBACT,UAAU;wBACV,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAET,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBACzB,8OAAC,oMAAM,CAAC,GAAG;gCAET,UAAU;gCACV,WAAU;0CAGV,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wIAAK;oDACJ,KAAK,QAAQ,KAAK;oDAClB,KAAK,QAAQ,KAAK;oDAClB,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,QAAQ,QAAQ;;;;;;;;;;;;;;;;;sDAMvB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;8DAEhB,8OAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;8DAGtB,8OAAC;oDAAO,WAAU;8DAA4H;;;;;;;;;;;;;;;;;;+BAhC7I,QAAQ,EAAE;;;;;;;;;;kCA0CrB,8OAAC,oMAAM,CAAC,GAAG;wBACT,UAAU;wBACV,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAET,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBACzB,8OAAC,oMAAM,CAAC,GAAG;gCAET,UAAU;gCACV,WAAU;0CAGV,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wIAAK;oDACJ,KAAK,QAAQ,KAAK;oDAClB,KAAK,QAAQ,KAAK;oDAClB,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,QAAQ,QAAQ;;;;;;;;;;;;;;;;;sDAKvB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;8DAEhB,8OAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;8DAGtB,8OAAC;oDAAO,WAAU;8DAA4H;;;;;;;;;;;;;;;;;;+BA/B7I,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;AA0C7B", "debugId": null}}]}