"use client";

import React from 'react';
import { motion } from 'framer-motion';

export interface TimelineEvent {
  id: string;
  date: string;
  title: string;
  description: string;
  icon?: string;
  category?: string;
  highlight?: boolean;
  image?: string;
}

interface TimelineProps {
  events: TimelineEvent[];
  title?: string;
  subtitle?: string;
  orientation?: 'vertical' | 'horizontal';
  variant?: 'default' | 'minimal' | 'detailed';
  showCategories?: boolean;
  className?: string;
}

const Timeline: React.FC<TimelineProps> = ({
  events,
  title = "Timeline",
  subtitle = "Key events and milestones",
  orientation = 'vertical',
  variant = 'default',
  showCategories = false,
  className = ""
}) => {
  const categories = showCategories ? [...new Set(events.map(e => e.category).filter(Boolean))] : [];

  return (
    <section className={`relative py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50/30 overflow-hidden ${className}`}>
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-20 left-20 w-80 h-80 bg-blue-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-cyan-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="relative container mx-auto px-4 lg:px-8 max-w-7xl">
        {/* Header */}
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 px-4 py-2 text-sm font-semibold tracking-wider text-blue-800 ring-1 ring-inset ring-blue-200 mb-6">
            ⏱️ {title}
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {title}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {subtitle}
          </p>
        </motion.div>

        {/* Categories Filter */}
        {showCategories && categories.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="flex flex-wrap justify-center gap-3 mb-12"
          >
            {categories.map((category, index) => (
              <span
                key={index}
                className="px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full text-sm font-medium text-gray-700 border border-gray-200 hover:bg-blue-50 hover:border-blue-200 transition-all duration-300"
              >
                {category}
              </span>
            ))}
          </motion.div>
        )}

        {/* Timeline */}
        {orientation === 'vertical' ? (
          <div className="relative">
            {/* Vertical line */}
            <div className="absolute left-8 md:left-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-200 via-blue-400 to-cyan-400 transform md:-translate-x-1/2"></div>

            {/* Events */}
            <div className="space-y-12">
              {events.map((event, index) => (
                <motion.div
                  key={event.id}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className={`relative flex items-center ${
                    index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
                  }`}
                >
                  {/* Content */}
                  <div className={`flex-1 ${index % 2 === 0 ? 'md:pr-8' : 'md:pl-8'}`}>
                    <div className="ml-16 md:ml-0">
                      <div className={`bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 group ${
                        event.highlight ? 'ring-2 ring-blue-200' : ''
                      }`}>
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <div className="text-sm font-semibold text-blue-600 mb-1">
                              {event.date}
                            </div>
                            {event.category && (
                              <span className="inline-block bg-blue-50 text-blue-700 text-xs font-medium px-2 py-1 rounded-full">
                                {event.category}
                              </span>
                            )}
                          </div>
                          {event.highlight && (
                            <div className="text-yellow-500">
                              ⭐
                            </div>
                          )}
                        </div>
                        <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                          {event.title}
                        </h3>
                        <p className="text-gray-600 leading-relaxed">
                          {event.description}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Timeline dot */}
                  <div className="absolute left-8 md:left-1/2 w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-white text-xl shadow-lg transform md:-translate-x-1/2 z-10">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-full animate-pulse opacity-75"></div>
                    <span className="relative z-10">{event.icon || '📅'}</span>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        ) : (
          // Horizontal Timeline
          <div className="relative">
            {/* Horizontal line */}
            <div className="hidden md:block absolute left-0 right-0 top-20 h-0.5 bg-gradient-to-r from-blue-200 via-blue-400 to-cyan-400"></div>

            {/* Events */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {events.map((event, index) => (
                <motion.div
                  key={event.id}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="relative"
                >
                  {/* Timeline dot */}
                  <div className=" md:block absolute top-0 left-1/2 w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-white transform -translate-x-1/2 -translate-y-6 z-10">
                    <span className="text-sm">{event.icon || '📅'}</span>
                  </div>

                  {/* Content */}
                  <div className={`bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 group mt-8 md:mt-12 ${
                    event.highlight ? 'ring-2 ring-blue-200' : ''
                  }`}>
                    <div className="text-sm font-semibold text-blue-600 mb-2">
                      {event.date}
                    </div>
                    {event.category && (
                      <span className="inline-block bg-blue-50 text-blue-700 text-xs font-medium px-2 py-1 rounded-full mb-3">
                        {event.category}
                      </span>
                    )}
                    <h3 className="text-lg font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                      {event.title}
                    </h3>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {event.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default Timeline;
