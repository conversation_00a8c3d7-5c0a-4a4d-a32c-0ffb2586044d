"use client";
import React from 'react';
import { motion } from "framer-motion";
import { Phone, Mail, Clock, MapPin, Send } from 'lucide-react';

// Animation variants for Framer Motion
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
    },
  },
};

const ContactPage = () => {
  const contactDetails = [
    { icon: <Phone size={18} />, label: "Phone", value: "+91-00000-00000" },
    { icon: <Mail size={18} />, label: "Email", value: "<EMAIL>" },
    { icon: <Clock size={18} />, label: "Hours", value: "24x7 Support" },
    { icon: <MapPin size={18} />, label: "Coverage", value: "Pan-India" },
  ];

  return (
    <section className="relative min-h-screen w-full overflow-hidden bg-gray-50">
      {/* Background Gradient */}
      <div className="absolute inset-0 -z-10 bg-gradient-to-br from-blue-50 via-white to-cyan-50" />
      
      <div className="mx-auto max-w-6xl px-4 pt-32 lg:pt-36 pb-24 sm:px-6 lg:px-8">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="text-center"
        >
          <motion.span 
            variants={itemVariants} 
            className="inline-flex items-center rounded-full bg-blue-100 px-4 py-1.5 text-sm font-semibold text-blue-700"
          >
            Contact Us
          </motion.span>
          <motion.h1 
            variants={itemVariants} 
            className="mt-4 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl"
          >
            Let's Start a Conversation
          </motion.h1>
          <motion.p 
            variants={itemVariants} 
            className="mx-auto mt-4 max-w-2xl text-lg text-gray-600"
          >
            We’re here to help. Send us a message, and our team will get back to you promptly.
          </motion.p>
        </motion.div>

        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          variants={containerVariants}
          className="mt-16 grid grid-cols-1 gap-8 md:grid-cols-3"
        >
          {/* Contact Details Card */}
          <motion.div 
            variants={itemVariants}
            className="rounded-2xl border border-gray-200/80 bg-white/70 p-8 shadow-sm backdrop-blur-sm"
          >
            <h3 className="text-xl font-semibold text-gray-900">Contact Information</h3>
            <p className="mt-1 text-gray-600">Find us here or drop a line.</p>
            <ul className="mt-6 space-y-4">
              {contactDetails.map((item, index) => (
                <li key={index} className="flex items-start gap-4">
                  <div className="flex-shrink-0 text-blue-600 mt-1">{item.icon}</div>
                  <div>
                    <span className="font-semibold text-gray-800">{item.label}:</span>
                    <span className="ml-2 text-gray-600">{item.value}</span>
                  </div>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Contact Form Card */}
          <motion.form
            variants={itemVariants}
            onSubmit={(e) => e.preventDefault()}
            className="md:col-span-2 rounded-2xl border border-gray-200/80 bg-white/70 p-8 shadow-sm backdrop-blur-sm"
          >
            <div className="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-2">
              <div>
                <label htmlFor="name" className="block text-sm font-medium leading-6 text-gray-800">Name</label>
                <input id="name" type="text" className="mt-1 block w-full rounded-md border-gray-300 bg-white/80 px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="Your name" required />
              </div>
              <div>
                <label htmlFor="email" className="block text-sm font-medium leading-6 text-gray-800">Email</label>
                <input id="email" type="email" className="mt-1 block w-full rounded-md border-gray-300 bg-white/80 px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="<EMAIL>" required />
              </div>
              <div>
                <label htmlFor="phone" className="block text-sm font-medium leading-6 text-gray-800">Phone</label>
                <input id="phone" type="tel" className="mt-1 block w-full rounded-md border-gray-300 bg-white/80 px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="+91-00000-00000" />
              </div>
              <div>
                <label htmlFor="subject" className="block text-sm font-medium leading-6 text-gray-800">Subject</label>
                <input id="subject" type="text" className="mt-1 block w-full rounded-md border-gray-300 bg-white/80 px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="How can we help?" />
              </div>
              <div className="sm:col-span-2">
                <label htmlFor="message" className="block text-sm font-medium leading-6 text-gray-800">Message</label>
                <textarea id="message" rows={5} className="mt-1 block w-full resize-y rounded-md border-gray-300 bg-white/80 px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="Briefly describe your inquiry..." required />
              </div>
            </div>
            <div className="mt-6">
              <button 
                type="submit" 
                className="inline-flex items-center justify-center gap-2 rounded-md bg-blue-600 px-6 py-3 text-sm font-semibold text-white shadow-sm transition-all hover:bg-blue-700 hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <Send size={16} />
                Send Message
              </button>
            </div>
          </motion.form>
        </motion.div>
      </div>
    </section>
  );
}

export default ContactPage;