"use client";

import React from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';

export function WhyChooseUsSection() {

  return (
    <section className="py-16 lg:py-24 bg-blue-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-14 lg:gap-20 items-center">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* Header */}
            <div className="mb-12">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Why Choose Us
              </h1>
              <p className="text-gray-600 text-lg">
                Our commitment to excellence and customer satisfaction
              </p>
            </div>

            {/* Numbered Points Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Point 01 */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
                className="space-y-4"
              >
                <div className="text-6xl font-black text-blue-400">01</div>
                <h3 className="text-xl font-bold text-gray-900">
                  Fast Breakdown Recovery
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  <span className="font-semibold text-gray-900">W</span>e understand the importance of breakdown recovery lead time to our customers.
                </p>
              </motion.div>

              {/* Point 02 */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
                className="space-y-4"
              >
                <div className="text-6xl font-black text-blue-400">02</div>
                <h3 className="text-xl font-bold text-gray-900">
                  Reliable System & Manpower
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  <span className="font-semibold text-gray-900">O</span>ur unique internal System, together with our dedicated manpower ensure that we deliver on time every time – We Won't Be Beaten.
                </p>
              </motion.div>

              {/* Point 03 */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
                className="space-y-4"
              >
                <div className="text-6xl font-black text-blue-400">03</div>
                <h3 className="text-xl font-bold text-gray-900">
                  ST HVAC Maintenance Method
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  <span className="font-semibold text-gray-900">O</span>ur unique 'ST HVAC Maintenance method is the quickest and safest process around, drastically reducing breakdown time and Maintenance costs.
                </p>
              </motion.div>

              {/* Point 04 */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                viewport={{ once: true }}
                className="space-y-4"
              >
                <div className="text-6xl font-black text-blue-400">04</div>
                <h3 className="text-xl font-bold text-gray-900">
                  Competitive Rates
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  <span className="font-semibold text-gray-900">O</span>ur Rates are very Competitive with no compromise attitude as far as quality is concerned.
                </p>
              </motion.div>
            </div>
          </motion.div>

          {/* Right Column - Large Image */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="relative  w-5/6 h-[500px] lg:h-[600px] rounded-2xl overflow-hidden shadow-2xl">
              <Image
                src="/background.jpg"
                alt="Professional HVAC Services"
                fill
                className="object-cover"
              />             
            </div>
          </motion.div>
        </div>


      </div>
    </section>
  );
}
