"use client";

import React from 'react';
import { motion } from 'framer-motion';

type TimelineItem = {
  year: string;
  title: string;
  description?: string;
  icon?: string;
  achievement?: string;
};

const defaultItems: TimelineItem[] = [
  {
    year: "2016",
    title: "Establishment",
    description: "Founded with a vision to provide specialized HVAC services across India.",
    icon: "🏗️",
    achievement: "Company Founded"
  },
  {
    year: "2017",
    title: "Incorporation",
    description: "Officially registered as a company in India, establishing our legal foundation.",
    icon: "📋",
    achievement: "Legal Entity"
  },
  {
    year: "2018",
    title: "Team Expansion",
    description: "Rapidly expanded our team to meet growing market demand and customer needs.",
    icon: "👥",
    achievement: "Team Growth"
  },
  {
    year: "2019",
    title: "Service Diversification",
    description: "Expanded into core HVAC equipment supply and comprehensive design services.",
    icon: "🔧",
    achievement: "Service Expansion"
  },
  {
    year: "2020",
    title: "Industry Recognition",
    description: "Became a trusted partner for Operations & Maintenance across multiple sectors.",
    icon: "🏆",
    achievement: "Trusted Partner"
  },
  {
    year: "2021-2024",
    title: "Continued Growth",
    description: "Established partnerships with 12+ brands and 150+ vendors nationwide.",
    icon: "🚀",
    achievement: "Market Leader"
  }
];

export function Journey({
  items = defaultItems,
  heading = "Our Journey",
  subheading = "Highlights from our growth and milestones over the years.",
}: {
  items?: TimelineItem[];
  heading?: string;
  subheading?: string;
}) {
  return (
    <section className="relative w-full py-20 bg-gradient-to-br from-slate-50 via-blue-50/20 to-slate-50 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-20 left-1/4 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-1/4 w-72 h-72 bg-cyan-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.header
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-16 text-center"
        >
          <div className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 px-4 py-2 text-sm font-semibold tracking-wider text-blue-800 ring-1 ring-inset ring-blue-200 mb-6">
            📈 Our Journey
          </div>
          <h2 className="text-4xl md:text-5xl font-bold tracking-tight text-gray-900 mb-6">
            {heading}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {subheading}
          </p>
        </motion.header>

        {/* Timeline */}
        <div className="relative">
          {/* Vertical line for mobile, horizontal for desktop */}
          <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-200 via-blue-400 to-cyan-400 md:hidden"></div>
          <div className="hidden md:block absolute left-0 right-0 top-1/2 h-0.5 bg-gradient-to-r from-blue-200 via-blue-400 to-cyan-400 transform -translate-y-1/2"></div>

          {/* Timeline Items */}
          <div className="space-y-12 md:space-y-0 md:grid md:grid-cols-3 md:gap-8 lg:grid-cols-6">
            {items.map((item, idx) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: idx * 0.2 }}
                viewport={{ once: true }}
                className="relative flex md:flex-col items-start md:items-center"
              >
                {/* Timeline dot */}
                <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-white text-2xl shadow-lg relative z-10 md:mb-6">
                  {item.icon}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-full animate-pulse opacity-75"></div>
                  <span className="relative z-10">{item.icon}</span>
                </div>

                {/* Content */}
                <div className="ml-6 md:ml-0 flex-1 md:text-center">
                  <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 group">
                    <div className="text-sm font-semibold text-blue-600 mb-2">
                      {item.year}
                    </div>
                    <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                      {item.title}
                    </h3>
                    {item.achievement && (
                      <div className="inline-block bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-800 text-xs font-medium px-3 py-1 rounded-full mb-3">
                        {item.achievement}
                      </div>
                    )}
                    {item.description && (
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {item.description}
                      </p>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-blue-600 to-cyan-600 rounded-3xl p-8 md:p-12 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">The Journey Continues</h3>
            <p className="text-lg opacity-90 max-w-2xl mx-auto">
              With each milestone, we've grown stronger and more committed to delivering exceptional HVAC services.
              Join us as we continue to innovate and expand our reach across India.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}

export default Journey;
