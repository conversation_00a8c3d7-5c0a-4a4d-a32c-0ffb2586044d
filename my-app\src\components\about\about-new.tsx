"use client";

import React from 'react'
import { motion } from 'framer-motion'
import Image from 'next/image'

const AboutSection = () => {
  const highlights = [
    {
      icon: "🕒",
      title: "24x7",
      subtitle: "Dedicated Support",
      description: "Round-the-clock service commitment"
    },
    {
      icon: "⚙️",
      title: "SITC",
      subtitle: "Complete Solutions",
      description: "Supply, Installation, Testing & Commissioning"
    },
    {
      icon: "👨‍🔧",
      title: "Skilled",
      subtitle: "Expert Technicians",
      description: "Equipped with tools and expertise"
    }
  ];

  const stats = [
    { number: "8+", label: "Years Experience" },
    { number: "150+", label: "Projects Completed" },
    { number: "100%", label: "Client Satisfaction" },
    { number: "24/7", label: "Support Available" }
  ];

  return (
    <section className="relative overflow-hidden pt-32 lg:pt-40 pb-32">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-cyan-50/30" />
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-20 w-96 h-96 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-cyan-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000"></div>
      </div>

      <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            <div className="space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 px-6 py-3 text-sm font-semibold tracking-wider text-blue-800 shadow-sm"
              >
                <span className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></span>
                About Us
              </motion.div>

              <motion.h1
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight tracking-tight text-gray-900"
              >
                S T HVAC
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600">
                  SERVICES
                </span>
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="text-xl text-gray-600 font-medium"
              >
                Installation, maintenance and end‑to‑end SITC solutions
              </motion.p>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="prose prose-lg prose-gray max-w-none space-y-6"
            >
              <p className="text-lg text-gray-700 leading-relaxed">
                We would like to take the opportunity tointroduceour selves for
                installation & maintenance of your HVAC & LT Electrical System.
                S T HVAC SERVICES is committed to serve you best, and will be
                stubborn on the point 24X7.
              </p>
              <p className="text-lg text-gray-700 leading-relaxed">
                Our Super skilled, Semi Skilled and Skilled Technicians will be
                equipped with all tools and tackles to meet the demand on time.
              </p>
              <p className="text-lg text-gray-700 leading-relaxed">
                We are committed to you in the strategic design and seamless
                execution of innovative, state-of-the-art technology HVAC
                systems that are delivered within budget and agreed time scales.
                We also operates an after sales support, providing functional
                and technological repair and maintenance services for their
                products.
              </p>
              <p className="text-lg text-gray-700 leading-relaxed">
                Starting from Supply, Installation, Commissioning & Testing
                (SITC), field and warranty support to on going help desk,
                carry-in repair and reverse logistics, S T HVAC delivers 'end to
                end' service solutions, designed to fulfil and enhance service
                level commitments, enabling partners to better serve their
                customers in an efficient and cost effective manner.
              </p>
            </motion.div>

            {/* Stats Grid */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-4"
            >
              {stats.map((stat, index) => (
                <div
                  key={index}
                  className="text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/20"
                >
                  <div className="text-2xl font-bold text-gray-900">
                    {stat.number}
                  </div>
                  <div className="text-sm text-gray-600 font-medium">
                    {stat.label}
                  </div>
                </div>
              ))}
            </motion.div>
          </motion.div>

          {/* Right Content - Image and Highlights */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="relative"
          >
            {/* Main Image */}
            <div className="relative h-[600px] rounded-3xl overflow-hidden shadow-2xl group">
              <Image
                src="/about1.jpg"
                alt="S T HVAC Services - Professional HVAC Installation"
                fill
                className="object-cover transition-transform duration-700 group-hover:scale-105"
                sizes="(max-width: 768px) 100vw, 50vw"
                priority
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
            </div>

            {/* Floating Highlights */}
            <div className="absolute -bottom-8 -left-8 right-8 grid grid-cols-1 gap-4">
              {highlights.map((highlight, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30, scale: 0.8 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
                  className="bg-white/95 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-white/20 group hover:shadow-2xl transition-all duration-300"
                >
                  <div className="flex items-center gap-4">
                    <div className="text-3xl group-hover:scale-110 transition-transform duration-300">
                      {highlight.icon}
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-gray-900">
                        {highlight.title}
                      </div>
                      <div className="text-sm font-semibold text-blue-600">
                        {highlight.subtitle}
                      </div>
                      <div className="text-xs text-gray-600">
                        {highlight.description}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}

export default AboutSection
