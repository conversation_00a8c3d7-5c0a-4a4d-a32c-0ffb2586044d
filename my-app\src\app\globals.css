@tailwind base;
@tailwind components;
@tailwind utilities;

/* Carousel Animation */
@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll {
  animation: scroll 60s linear infinite;
}

/* Custom Properties */
:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  /* Updated primary to vibrant orange for the header design */
  --primary: oklch(41.198% 0.20815 265.511);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  /* Added custom orange color variables for the header */
  --header-orange: oklch(0.65 0.25 35);
  --header-white: oklch(1 0 0);
  --header-text-dark: oklch(0.145 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}
/* Base Styles */
html {
}

/* Modern sticky header helpers */
.header-shadow {
  box-shadow: 0 10px 30px -10px rgba(0,0,0,0.12);
}

.header-border {
  border-bottom: 1px solid rgba(255,255,255,0.2);
}

@layer base {
  * {
    @apply border-gray-200 outline-offset-2;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Enhanced Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes zoomInOut {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Animation Classes */
.animate-fadeIn {
  animation: fadeIn 0.8s ease-out forwards;
}

.animate-slideIn {
  animation: slideIn 0.8s ease-out forwards;
}

.animate-slideInRight {
  animation: slideInRight 0.8s ease-out forwards;
}

.animate-scaleIn {
  animation: scaleIn 0.6s ease-out forwards;
}

.animate-bounce {
  animation: bounce 2s infinite;
}

.animate-bounce-slow {
  animation: bounce 3s infinite;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-zoomInOut {
  animation: zoomInOut 8s ease-in-out infinite;
}

/* Animation Delays */
.animate-delay-100 {
  animation-delay: 100ms;
}

.animate-delay-200 {
  animation-delay: 200ms;
}

.animate-delay-300 {
  animation-delay: 300ms;
}

.animate-delay-400 {
  animation-delay: 400ms;
}

.animate-delay-500 {
  animation-delay: 500ms;
}

.animate-delay-700 {
  animation-delay: 700ms;
}

.animate-delay-1000 {
  animation-delay: 1000ms;
}

/* Custom Gradient Backgrounds */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.bg-gradient-accent {
  background: linear-gradient(135deg, var(--accent-color), #F59E0B);
}

.bg-gradient-dark {
  background: linear-gradient(135deg, #1F2937, #374151);
}

/* Enhanced Button Styles */
.btn-primary {
  @apply bg-gradient-to-r from-[#3B1FC2] to-purple-600 text-white font-bold py-4 px-8 rounded-full transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 hover:from-purple-600 hover:to-[#3B1FC2];
}

.btn-secondary {
  @apply border-2 border-[#3B1FC2] text-[#3B1FC2] font-bold py-4 px-8 rounded-full transition-all duration-300 hover:bg-[#3B1FC2] hover:text-white transform hover:scale-105;
}

.btn-accent {
  @apply bg-gradient-to-r from-yellow-400 to-orange-500 text-black font-bold py-4 px-8 rounded-full transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105;
}

/* Card Styles */
.card {
  @apply bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1;
}

.card-elevated {
  @apply bg-white rounded-3xl shadow-2xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2;
}

/* Glass Morphism Effect */
.glass {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  backdrop-filter: blur(10px);
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Text Gradient */
.text-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-accent {
  background: linear-gradient(135deg, var(--accent-color), #F59E0B);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Hover Effects */
.hover-lift {
  @apply transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl;
}

.hover-scale {
  @apply transition-all duration-300 transform hover:scale-105;
}

.hover-glow {
  @apply transition-all duration-300;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 31, 194, 0.3);
}

/* Loading States */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.loading-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Form Enhancements */
.form-input {
  @apply w-full px-6 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#3B1FC2] focus:border-[#3B1FC2] outline-none transition-all duration-300;
}

.form-input:focus {
  box-shadow: 0 0 0 3px rgba(59, 31, 194, 0.1);
}

.form-textarea {
  @apply w-full px-6 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#3B1FC2] focus:border-[#3B1FC2] outline-none transition-all duration-300 resize-y;
}

.form-select {
  @apply w-full px-6 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#3B1FC2] focus:border-[#3B1FC2] outline-none transition-all duration-300 bg-white;
}


/* Responsive Typography */
.text-responsive-xl {
  @apply text-3xl md:text-4xl lg:text-5xl xl:text-6xl;
}

.text-responsive-lg {
  @apply text-2xl md:text-3xl lg:text-4xl;
}

.text-responsive-md {
  @apply text-xl md:text-2xl lg:text-3xl;
}

/* Section Spacing */
.section-padding {
  @apply py-16 md:py-20 lg:py-24;
}

.container-custom {
  @apply container mx-auto px-4 sm:px-6 lg:px-8;
}

/* Image Overlay Effects */
.image-overlay {
  position: relative;
}

.image-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 31, 194, 0.8), rgba(124, 58, 237, 0.6));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.image-overlay:hover::before {
  opacity: 1;
}

/* Border Utilities */
.border-gradient {
  border: 2px solid;
  border-image: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) 1;
}

/* Custom Shadows */
.shadow-custom {
  box-shadow: 0 10px 25px -5px rgba(59, 31, 194, 0.1), 0 10px 10px -5px rgba(59, 31, 194, 0.04);
}

.shadow-custom-lg {
  box-shadow: 0 20px 40px -10px rgba(59, 31, 194, 0.15), 0 10px 20px -5px rgba(59, 31, 194, 0.08);
}

.shadow-custom-xl {
  box-shadow: 0 25px 50px -15px rgba(59, 31, 194, 0.2), 0 15px 30px -10px rgba(59, 31, 194, 0.1);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .dark-mode {
    --text-dark: #F9FAFB;
    --text-light: #D1D5DB;
    --bg-light: #1F2937;
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
}

/* Accessibility Enhancements */
.focus-visible {
  @apply focus:outline-none focus:ring-2 focus:ring-[#3B1FC2] focus:ring-offset-2;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .high-contrast {
    --primary-color: #0000FF;
    --text-dark: #000000;
    --text-light: #000000;
  }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .mobile-padding {
    @apply px-4;
  }
  
  .mobile-text {
    @apply text-sm;
  }
  
  .mobile-hidden {
    @apply hidden;
  }
}

/* Performance Optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

.optimize-rendering {
  contain: layout style paint;
}

/* Utility Classes */
.aspect-golden {
  aspect-ratio: 1.618 / 1;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

/* Brand Colors */
.text-brand-primary {
  color: var(--primary-color);
}

.text-brand-secondary {
  color: var(--secondary-color);
}

.text-brand-accent {
  color: var(--accent-color);
}

.bg-brand-primary {
  background-color: var(--primary-color);
}

.bg-brand-secondary {
  background-color: var(--secondary-color);
}

.bg-brand-accent {
  background-color: var(--accent-color);
}
