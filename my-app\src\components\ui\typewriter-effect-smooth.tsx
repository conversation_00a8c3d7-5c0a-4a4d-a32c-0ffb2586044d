"use client";

import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";

export const TypewriterEffectSmooth = ({
  words,
  className,
  cursorClassName,
}: {
  words: {
    text: string;
    className?: string;
  }[];
  className?: string;
  cursorClassName?: string;
}) => {
  const [displayedText, setDisplayedText] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);

  // Combine all words into one continuous text
  const fullText = words.map(word => word.text).join(" ");
  const firstWordClass = words[0]?.className || "";

  useEffect(() => {
    const timeout = setTimeout(() => {
      if (currentIndex < fullText.length) {
        // Typing the full sentence
        setDisplayedText(fullText.substring(0, currentIndex + 1));
        setCurrentIndex(currentIndex + 1);
      }
      // Once fully typed, keep it displayed (no deleting or cycling)
    }, 100); // Typing speed

    return () => clearTimeout(timeout);
  }, [currentIndex, fullText]);

  return (
    <div className={cn("flex items-center gap-1", className)}>
      <div className="text-xs sm:text-base md:text-xl lg:text-3xl xl:text-5xl font-bold">
        <span className={cn("text-blue-800 font-medium italic", firstWordClass)}>
          {displayedText}
        </span>
        <span 
          className={cn(
            "inline-block w-0.5 h-4 sm:h-6 xl:h-12 bg-blue-800 ml-1 animate-pulse",
            cursorClassName
          )}
        />
      </div>
    </div>
  );
};
