"use client";
import React from "react";
import { motion } from "framer-motion";
import { LayoutGrid } from "@/components/ui/layout-grid";

export default function Gallery() {
  return (
    <section className="relative py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50 overflow-hidden">
      {/* Background Blobs */}
      <div className="absolute inset-0 opacity-30 pointer-events-none">
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-72 h-72 bg-cyan-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            OUR <span className="text-blue-600">PROJECT</span> GALLERY
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Explore our portfolio of successful HVAC installations and
            energy-efficient projects.
          </p>
        </motion.div>

        {/* Gallery Grid */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="min-h-screen"
        >
          <LayoutGrid cards={cards} />
        </motion.div>
      </div>
    </section>
  );
}

//
// 🔹 Text Blocks for Each Card
//
const SkeletonOne = () => (
  <div>
    <p className="font-bold md:text-4xl text-xl text-white">
      Commercial HVAC Installation
    </p>
    <p className="font-normal text-base text-white">
      Downtown Office Complex — 2024
    </p>
    <p className="font-normal text-base my-4 max-w-lg text-neutral-200">
      Complete HVAC system setup for a 50,000 sq ft office building, featuring
      high-efficiency units and smart climate control systems.
    </p>
  </div>
);

const SkeletonTwo = () => (
  <div>
    <p className="font-bold md:text-4xl text-xl text-white">
      Industrial Ductwork System
    </p>
    <p className="font-normal text-base text-white">
      Manufacturing Plant — 2023
    </p>
    <p className="font-normal text-base my-4 max-w-lg text-neutral-200">
      Custom-engineered ductwork and ventilation designed for heavy-duty
      manufacturing environments.
    </p>
  </div>
);

const SkeletonThree = () => (
  <div>
    <p className="font-bold md:text-4xl text-xl text-white">
      Smart Thermostat Installation
    </p>
    <p className="font-normal text-base text-white">Residential Home — 2024</p>
    <p className="font-normal text-base my-4 max-w-lg text-neutral-200">
      Integrated smart thermostat system with app-based monitoring and energy
      optimization.
    </p>
  </div>
);

const SkeletonFour = () => (
  <div>
    <p className="font-bold md:text-4xl text-xl text-white">
      Residential HVAC Upgrade
    </p>
    <p className="font-normal text-base text-white">Family Residence — 2024</p>
    <p className="font-normal text-base my-4 max-w-lg text-neutral-200">
      Complete upgrade to high-efficiency units with improved indoor air quality
      and quieter performance.
    </p>
  </div>
);

const SkeletonFive = () => (
  <div>
    <p className="font-bold md:text-4xl text-xl text-white">
      Preventive Maintenance Service
    </p>
    <p className="font-normal text-base text-white">Various Locations — 2024</p>
    <p className="font-normal text-base my-4 max-w-lg text-neutral-200">
      Comprehensive maintenance plans including inspection, cleaning, and
      performance optimization.
    </p>
  </div>
);

const SkeletonSix = () => (
  <div>
    <p className="font-bold md:text-4xl text-xl text-white">
      Energy Efficiency Retrofit
    </p>
    <p className="font-normal text-base text-white">Retail Center — 2023</p>
    <p className="font-normal text-base my-4 max-w-lg text-neutral-200">
      Energy-efficiency retrofitting project achieving 30 % reduction in overall
      HVAC power usage.
    </p>
  </div>
);

//
// 🔹 Card Data (connects to layout-grid.tsx)
//
const cards = [
  {
    id: 1,
    content: <SkeletonOne />,
    className: "md:col-span-2",
    thumbnail: "/background2.jpg",
  },
  {
    id: 2,
    content: <SkeletonTwo />,
    className: "col-span-1",
    thumbnail: "/background3.jpg",
  },
  {
    id: 3,
    content: <SkeletonThree />,
    className: "col-span-1",
    thumbnail: "/background2.jpg",
  },
  {
    id: 4,
    content: <SkeletonFour />,
    className: "md:col-span-2",
    thumbnail: "/background.jpg",
  },
  {
    id: 5,
    content: <SkeletonFive />,
    className: "col-span-1",
    thumbnail: "/Picture8.jpg",
  },
  {
    id: 6,
    content: <SkeletonSix />,
    className: "col-span-1",
    thumbnail: "/Picture11.jpg",
  },
];
