"use client";
import React from "react";
import { motion } from "motion/react";
import { LayoutGrid } from "@/components/ui/layout-grid";

const cards = [
  {
    id: 1,
    content: (
      <div className="text-white">
        <div className="flex items-center mb-3">
          <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mr-3">
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className="text-3xl font-bold">Cooling Solutions</h2>
        </div>
        <p className="text-sm opacity-90 leading-relaxed">
          Efficient and eco-friendly HVAC systems for residential and commercial
          spaces. Advanced cooling technology that reduces energy consumption.
        </p>
        <div className="mt-4 flex items-center text-xs opacity-75">
          <span className="bg-blue-500/20 px-2 py-1 rounded-full mr-2">Energy Efficient</span>
          <span className="bg-green-500/20 px-2 py-1 rounded-full">Eco-Friendly</span>
        </div>
      </div>
    ),
    className: "col-span-1 md:col-span-1 h-[400px]",
    thumbnail: "/background.jpg",
  },
  {
    id: 2,
    content: (
      <div className="text-white">
        <div className="flex items-center mb-3">
          <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center mr-3">
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className="text-3xl font-bold">Heating Systems</h2>
        </div>
        <p className="text-sm opacity-90 leading-relaxed">
          High-efficiency heating that ensures comfort while saving energy.
          Smart thermostats and zone control for optimal temperature management.
        </p>
        <div className="mt-4 flex items-center text-xs opacity-75">
          <span className="bg-red-500/20 px-2 py-1 rounded-full mr-2">High Efficiency</span>
          <span className="bg-orange-500/20 px-2 py-1 rounded-full">Smart Control</span>
        </div>
      </div>
    ),
    className: "col-span-1 md:col-span-2 h-[400px]",
    thumbnail: "/background2.jpg",
  },
  {
    id: 3,
    content: (
      <div className="text-white">
        <div className="flex items-center mb-3">
          <div className="w-12 h-12 bg-cyan-500 rounded-full flex items-center justify-center mr-3">
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zM3 15a1 1 0 011-1h1a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1v-1zm6-11a1 1 0 011-1h4a1 1 0 011 1v7a1 1 0 01-1 1h-4a1 1 0 01-1-1V4zm6 10a1 1 0 011-1h1a1 1 0 011 1v1a1 1 0 01-1 1h-1a1 1 0 01-1-1v-1z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className="text-3xl font-bold">Ventilation Design</h2>
        </div>
        <p className="text-sm opacity-90 leading-relaxed">
          Airflow systems designed for maximum air quality and thermal balance.
          Custom ventilation solutions for any space configuration.
        </p>
        <div className="mt-4 flex items-center text-xs opacity-75">
          <span className="bg-cyan-500/20 px-2 py-1 rounded-full mr-2">Air Quality</span>
          <span className="bg-teal-500/20 px-2 py-1 rounded-full">Custom Design</span>
        </div>
      </div>
    ),
    className: "col-span-1 md:col-span-1 h-[400px]",
    thumbnail: "/background3.jpg",
  },
  {
    id: 4,
    content: (
      <div className="text-white">
        <div className="flex items-center mb-3">
          <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mr-3">
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className="text-3xl font-bold">Smart Automation</h2>
        </div>
        <p className="text-sm opacity-90 leading-relaxed">
          IoT-integrated HVAC controls — manage temperature, humidity, and
          energy from anywhere. Advanced automation for maximum efficiency.
        </p>
        <div className="mt-4 flex items-center text-xs opacity-75">
          <span className="bg-purple-500/20 px-2 py-1 rounded-full mr-2">IoT Enabled</span>
          <span className="bg-indigo-500/20 px-2 py-1 rounded-full">Remote Control</span>
        </div>
      </div>
    ),
    className: "col-span-1 md:col-span-2 h-[400px]",
    thumbnail: "/background.jpg",
  },
  {
    id: 5,
    content: (
      <div className="text-white">
        <div className="flex items-center mb-3">
          <div className="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center mr-3">
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className="text-3xl font-bold">Ductwork Fabrication</h2>
        </div>
        <p className="text-sm opacity-90 leading-relaxed">
          Custom ductwork solutions for precise airflow and optimal performance.
          Professional fabrication with premium materials and precision engineering.
        </p>
        <div className="mt-4 flex items-center text-xs opacity-75">
          <span className="bg-yellow-500/20 px-2 py-1 rounded-full mr-2">Custom Built</span>
          <span className="bg-amber-500/20 px-2 py-1 rounded-full">Precision</span>
        </div>
      </div>
    ),
    className: "col-span-1 md:col-span-1 h-[400px]",
    thumbnail: "/background2.jpg",
  },
  {
    id: 6,
    content: (
      <div className="text-white">
        <div className="flex items-center mb-3">
          <div className="w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-3">
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className="text-3xl font-bold">Maintenance & Service</h2>
        </div>
        <p className="text-sm opacity-90 leading-relaxed">
          Comprehensive maintenance programs and 24/7 emergency service support.
          Preventive care to extend system life and ensure peak performance.
        </p>
        <div className="mt-4 flex items-center text-xs opacity-75">
          <span className="bg-emerald-500/20 px-2 py-1 rounded-full mr-2">24/7 Support</span>
          <span className="bg-green-500/20 px-2 py-1 rounded-full">Preventive Care</span>
        </div>
      </div>
    ),
    className: "col-span-1 md:col-span-2 h-[400px]",
    thumbnail: "/background3.jpg",
  },
];

export default function Gallery() {
  return (
    <section className="relative py-32 bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50 overflow-hidden">
      {/* Enhanced Background Elements */}
      <motion.div
        className="absolute -top-40 -left-40 w-[500px] h-[500px] rounded-full bg-gradient-to-r from-blue-200/40 to-cyan-200/40 blur-3xl"
        animate={{
          x: [0, 60, 0],
          y: [0, 40, 0],
          scale: [1, 1.1, 1]
        }}
        transition={{ duration: 15, repeat: Infinity, ease: "easeInOut" }}
      />
      <motion.div
        className="absolute top-1/2 -right-40 w-[600px] h-[600px] bg-gradient-to-l from-purple-200/30 to-pink-200/30 rounded-full blur-3xl"
        animate={{
          y: [0, -50, 0],
          x: [0, -30, 0],
          rotate: [0, 180, 360]
        }}
        transition={{ duration: 20, repeat: Infinity, ease: "easeInOut" }}
      />
      <motion.div
        className="absolute bottom-0 left-1/4 w-[400px] h-[400px] bg-gradient-to-t from-emerald-200/25 to-teal-200/25 rounded-full blur-3xl"
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.5, 0.3]
        }}
        transition={{ duration: 12, repeat: Infinity, ease: "easeInOut" }}
      />

      {/* Floating particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-blue-400/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -100, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 8 + Math.random() * 4,
              repeat: Infinity,
              delay: Math.random() * 5,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      {/* Enhanced Title Section */}
      <motion.div
        className="text-center mb-20 relative z-20"
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
      >
        <motion.div
          className="inline-block mb-4"
          whileHover={{ scale: 1.05 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <span className="inline-block px-4 py-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full text-sm font-medium text-blue-600 border border-blue-200/50 backdrop-blur-sm">
            Our Work Portfolio
          </span>
        </motion.div>
        <motion.h2
          className="text-6xl md:text-7xl font-bold bg-gradient-to-r from-gray-800 via-blue-600 to-purple-600 bg-clip-text text-transparent mb-6"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          Our Gallery
        </motion.h2>
        <motion.p
          className="text-gray-600 max-w-2xl mx-auto text-lg leading-relaxed"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          Explore our comprehensive portfolio of HVAC projects, innovative environmental
          systems, and cutting-edge climate control solutions that transform spaces.
        </motion.p>
      </motion.div>

      {/* Enhanced Layout Grid */}
      <motion.div
        className="relative z-30"
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 1, delay: 0.6 }}
        viewport={{ once: true }}
      >
        <LayoutGrid cards={cards} />
      </motion.div>

      {/* Bottom decorative element */}
      <motion.div
        className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-white/50 to-transparent pointer-events-none"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1 }}
        viewport={{ once: true }}
      />
    </section>
  );
}
