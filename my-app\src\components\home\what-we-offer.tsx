"use client";

import React from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';

export function WhatWeOfferSection() {
  const services = [
    {
      id: 1,
      title: "HVAC System Installation",
      description:
        "Heating, ventilation, andair conditioning (HVAC) system is designed to achieve the environmental requirements of the comfort of occupants and a process.",
      image: "/offer1.jpg",
      category: "Installation",
    },
    {
      id: 2,
      title: "Comfort Cooling Solutions",
      description:
        "These systems designed to give you  maximum comfort. Be it in an office  environment or even in a hotel.",
      image: "/offer2.jpg",
      category: "Cooling",
    },
    {
      id: 3,
      title: "Mechanical Ventilation",
      description:
        "These are ideal for factories where  ventilations is key for both goods and  factory workers",
      image: "/offer3.jpg",
      category: "Ventilation",
    },
    {
      id: 4,
      title: "Temperature Monitoring",
      description:
        "These are solutions designed for  sensitive areas where awareness of  temperature and humidity status at any  given time is of utmost importance.",
      image: "/offer4.jpg",
      category: "Monitoring",
    },
    {
      id: 5,
      title: "HVAC Plant Operations & Maintenance",
      description:
        "Operation and maintenance of HVAC plant maintaining log books, generating reports, making energy efficient system.",
      image: "/offer5.jpg",
      category: "Maintenance",
    },
    {
      id: 6,
      title: "Building Flush Out",
      description:
        "This is ideal for sensitive areas such as: Offices  data centers, hospital laboratories,  theatres e.t.c",
      image: "/offer6.jpg",
      category: "Air Quality",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <section className="relative py-20 bg-gray-50 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-11.046-8.954-20-20-20v20h20z'/%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: "40px 40px",
          }}
        ></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center py-2">
            <div className="w-16 h-px bg-blue-600"></div>
            <span className="px-4 text-blue-600 font-medium text-sm uppercase tracking-wider">Services</span>
            <div className="w-16 h-px bg-blue-600"></div>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold tracking-tight text-gray-900 mb-6">
            What <span className="text-blue-600">We</span> Offer
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Trust our expertise to deliver fast, reliable results every time.
            Our skilled technicians ensure your comfort is never compromised
            with professional solutions tailored to your needs.
          </p>
        </motion.div>

        {/* Services Grid - 3 Cards per Row */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12"
        >
          {services.slice(0, 3).map((service) => (
            <motion.div
              key={service.id}
              variants={itemVariants}
              className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100"
            >
              {/* Horizontal Card Layout */}
              <div className="flex flex-col h-full">
                {/* Image Section */}
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={service.image}
                    alt={service.title}
                    width={400}
                    height={200}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                  <div className="absolute top-4 left-4">
                    <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                      {service.category}
                    </span>
                  </div>
                </div>

                {/* Content Section */}
                <div className="p-6 flex-1 flex flex-col">
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 mb-4 leading-relaxed flex-1">
                    {service.description}
                  </p>
                  {/* Action Button */}
                  <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-semibold transition-colors duration-300 mt-auto">
                    Learn More
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Second Row - 3 Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8"
        >
          {services.slice(3, 6).map((service) => (
            <motion.div
              key={service.id}
              variants={itemVariants}
              className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100"
            >
              {/* Horizontal Card Layout */}
              <div className="flex flex-col h-full">
                {/* Image Section */}
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={service.image}
                    alt={service.title}
                    width={400}
                    height={200}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                  <div className="absolute top-4 left-4">
                    <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                      {service.category}
                    </span>
                  </div>
                </div>
                {/* Content Section */}
                <div className="p-6 flex-1 flex flex-col">
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 mb-4 leading-relaxed flex-1">
                    {service.description}
                  </p>
                  {/* Action Button */}
                  <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-semibold transition-colors duration-300 mt-auto">
                    Learn More
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}