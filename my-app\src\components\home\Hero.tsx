"use client";

import React, { useState, useEffect } from "react";
import { TypewriterEffectSmooth } from "@/components/ui/typewriter-effect-smooth";
import Image from "next/image";
import Link from "next/link";

const Hero: React.FC = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [currentBg, setCurrentBg] = useState(0);

  // Typewriter animation words
  const words = [
    { text: "Breathe", className: "text-white font-bold" },
    { text: "Easy...", className: "text-blue-400 font-bold" },
    { text: "Live", className: "text-white font-bold" },
    { text: "Easy...", className: "text-cyan-400 font-bold" },
  ];

  // Background rotation
  const backgrounds = ["/background3.jpg", "/background2.jpg"];

  useEffect(() => {
    setIsLoaded(true);
    const bgInterval = setInterval(() => {
      setCurrentBg((prev) => (prev + 1) % backgrounds.length);
    }, 8000);
    return () => clearInterval(bgInterval);
  }, [backgrounds.length]);

  return (
    <section className="relative min-h-screen rounded-xl mx-14 flex items-center justify-center overflow-hidden">
      {/* Background with Parallax Effect */}
      <div className="absolute inset-0 bg-fixed">
        {backgrounds.map((bg, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-all duration-2000 ${
              index === currentBg
                ? "opacity-100 scale-100"
                : "opacity-0 scale-105"
            }`}
          >
            <Image
              src={bg}
              alt="HVAC Background"
              fill
              className="object-cover"
              priority={index === 0}
            />
          </div>
        ))}

        {/* Modern Overlay */}
        <div className="absolute inset-0 bg-transparent " />
      </div>

      {/* Main Content */}
      <div
        className={`relative z-10 w-full max-w-6xl mx-auto px-6 transition-all duration-1000 ${
          isLoaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
        }`}
      >
        {/* Hero Grid Layout */}
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
          {/* Left Column - Content */}
          <div className="text-white space-y-8">
            {/* Main Heading */}
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight">
              <span className="text-white">ST HVAC  SALES & SERVICES</span>
            </h1>

            {/* Typewriter */}
            <div className="text-xl sm:text-2xl">
              <TypewriterEffectSmooth
                words={words}
                className="text-xl sm:text-2xl lg:text-3xl"
                cursorClassName="bg-cyan-400"
              />
            </div>

            {/* Description */}
            <p className="text-lg text-gray-300 leading-relaxed max-w-lg">
              Expert heating, cooling, and air quality solutions. From emergency
              repairs to complete system installations, we deliver reliable
              comfort for your home and business.
            </p>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href="/about"
                className="px-8 py-4 bg-blue-600  text-white rounded-lg font-semibold transition-all duration-300 flex items-center justify-center gap-3 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                Learn More
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Background Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2 z-20">
        {backgrounds.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentBg(index)}
            className={`w-2 h-2 rounded-full transition-all duration-300 ${
              index === currentBg
                ? "bg-blue-600 w-8"
                : "bg-white/40 hover:bg-white/60"
            }`}
          />
        ))}
      </div>
    </section>
  );
};

export default Hero;
