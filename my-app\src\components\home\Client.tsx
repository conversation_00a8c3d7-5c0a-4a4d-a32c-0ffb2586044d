'use client'

import React from 'react'
import Image from 'next/image'

const Client = () => {
  const clientImages = Array.from({ length: 29 }, (_, i) => `client${i + 1}.jpg`)

  // Duplicate the array to create seamless loop
  const duplicatedImages = [...clientImages, ...clientImages]

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Clients</h2>
          <p className="text-lg text-gray-600">
            Trusted by leading companies and organizations
          </p>
        </div>

        <div className="relative overflow-hidden">
          <div className="flex animate-scroll">
            {duplicatedImages.map((image, index) => (
              <div
                key={index}
                className="flex-shrink-0 w-32 h-20 mx-4 relative"
              >
                <Image
                  src={`/${image}`}
                  alt={`Client ${(index % clientImages.length) + 1}`}
                  fill
                  className="object-contain filter grayscale hover:grayscale-0 transition-all duration-300"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default Client